{"rustc": 12610991425282158916, "features": "[\"default\"]", "declared_features": "[\"default\", \"dynamic-keys\", \"nested-values\"]", "target": 12992234790552270850, "profile": 8276155916380437441, "path": 6115337218660229428, "deps": [[501918078635137462, "take_mut", false, 13924975805539456908], [1760363864088255996, "build_script_build", false, 5014974772040669494], [2337077568557944517, "slog", false, 6894067813239298079], [9727213718512686088, "crossbeam_channel", false, 8881916974937473710], [12427285511609802057, "thread_local", false, 11185379965283501049]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/slog-async-9a27994ed35c9179/dep-lib-slog_async", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
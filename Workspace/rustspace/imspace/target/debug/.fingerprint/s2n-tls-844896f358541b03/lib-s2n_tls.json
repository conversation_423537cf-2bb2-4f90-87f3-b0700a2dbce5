{"rustc": 12610991425282158916, "features": "[\"default\", \"quic\"]", "declared_features": "[\"default\", \"fips\", \"pq\", \"quic\", \"unstable-cert_authorities\", \"unstable-fingerprint\", \"unstable-ktls\", \"unstable-renegotiate\", \"unstable-testing\"]", "target": 1936344333877211033, "profile": 8276155916380437441, "path": 11735101182798532067, "deps": [[530211389790465181, "hex", false, 17801719985305944747], [1906322745568073236, "pin_project_lite", false, 18099131382932808050], [2924422107542798392, "libc", false, 8303689818531122296], [10087617178487351952, "s2n_tls_sys", false, 9813138231578884111], [14633813869673313769, "errno", false, 9577179836725517385], [17288211034397652384, "build_script_build", false, 16019072274724461701]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/s2n-tls-844896f358541b03/dep-lib-s2n_tls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
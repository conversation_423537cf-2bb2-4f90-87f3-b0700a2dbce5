{"rustc": 12610991425282158916, "features": "[\"attributes\", \"default\", \"log\", \"std\", \"tracing-attributes\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 18211135587835468629, "path": 762668586801356622, "deps": [[1906322745568073236, "pin_project_lite", false, 10264941928206167718], [2967683870285097694, "tracing_attributes", false, 13424623272567807969], [5986029879202738730, "log", false, 7893993066555791027], [11033263105862272874, "tracing_core", false, 13258853160600865663]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-cc1f74b7fc073f0b/dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
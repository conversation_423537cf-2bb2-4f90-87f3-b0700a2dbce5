{"$message_type":"diagnostic","message":"expected function, found `impl nom::Parser<_, Output = _, Error = _>`","code":{"code":"E0618","explanation":"Attempted to call something which isn't a function nor a method.\n\nErroneous code examples:\n\n```compile_fail,E0618\nenum X {\n    Entry,\n}\n\nX::Entry(); // error: expected function, tuple struct or tuple variant,\n            // found `X::Entry`\n\n// Or even simpler:\nlet x = 0i32;\nx(); // error: expected function, tuple struct or tuple variant, found `i32`\n```\n\nOnly functions and methods can be called using `()`. Example:\n\n```\n// We declare a function:\nfn i_am_a_function() {}\n\n// And we call it:\ni_am_a_function();\n```\n"},"level":"error","spans":[{"file_name":"bitutils/src/imcmd.rs","byte_start":2694,"byte_end":2750,"line_start":76,"line_end":76,"column_start":25,"column_end":81,"is_primary":false,"text":[{"text":"    let (input, text) = delimited(tag(\"\\\"\"), take_until(\"\\\"\"), tag(\"\\\"\"))(input)?;","highlight_start":25,"highlight_end":81}],"label":"call expression requires function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"bitutils/src/imcmd.rs","byte_start":2694,"byte_end":2743,"line_start":76,"line_end":76,"column_start":25,"column_end":74,"is_primary":true,"text":[{"text":"    let (input, text) = delimited(tag(\"\\\"\"), take_until(\"\\\"\"), tag(\"\\\"\"))(input)?;","highlight_start":25,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0618]\u001b[0m\u001b[0m\u001b[1m: expected function, found `impl nom::Parser<_, Output = _, Error = _>`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mbitutils/src/imcmd.rs:76:25\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m76\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let (input, text) = delimited(tag(\"\\\"\"), take_until(\"\\\"\"), tag(\"\\\"\"))(input)?;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mcall expression requires function\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"expected function, found `Choice<(fn(&str) -> ... {parse_login}, ..., ..., ..., ...)>`","code":{"code":"E0618","explanation":"Attempted to call something which isn't a function nor a method.\n\nErroneous code examples:\n\n```compile_fail,E0618\nenum X {\n    Entry,\n}\n\nX::Entry(); // error: expected function, tuple struct or tuple variant,\n            // found `X::Entry`\n\n// Or even simpler:\nlet x = 0i32;\nx(); // error: expected function, tuple struct or tuple variant, found `i32`\n```\n\nOnly functions and methods can be called using `()`. Example:\n\n```\n// We declare a function:\nfn i_am_a_function() {}\n\n// And we call it:\ni_am_a_function();\n```\n"},"level":"error","spans":[{"file_name":"bitutils/src/imcmd.rs","byte_start":4234,"byte_end":4356,"line_start":119,"line_end":125,"column_start":5,"column_end":14,"is_primary":false,"text":[{"text":"    alt((","highlight_start":5,"highlight_end":10},{"text":"        parse_login,","highlight_start":1,"highlight_end":21},{"text":"        parse_send,","highlight_start":1,"highlight_end":20},{"text":"        parse_logout,","highlight_start":1,"highlight_end":22},{"text":"        parse_exit,","highlight_start":1,"highlight_end":20},{"text":"        parse_quit,","highlight_start":1,"highlight_end":20},{"text":"    ))(input)","highlight_start":1,"highlight_end":14}],"label":"call expression requires function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"bitutils/src/imcmd.rs","byte_start":4234,"byte_end":4349,"line_start":119,"line_end":125,"column_start":5,"column_end":7,"is_primary":true,"text":[{"text":"    alt((","highlight_start":5,"highlight_end":10},{"text":"        parse_login,","highlight_start":1,"highlight_end":21},{"text":"        parse_send,","highlight_start":1,"highlight_end":20},{"text":"        parse_logout,","highlight_start":1,"highlight_end":22},{"text":"        parse_exit,","highlight_start":1,"highlight_end":20},{"text":"        parse_quit,","highlight_start":1,"highlight_end":20},{"text":"    ))(input)","highlight_start":1,"highlight_end":7}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the full name for the type has been written to '/Users/<USER>/Workspace/rustspace/imspace/target/debug/deps/bitutils-9158ca389b4edb72.long-type-6918928733642237960.txt'","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider using `--verbose` to print the full type name to the console","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0618]\u001b[0m\u001b[0m\u001b[1m: expected function, found `Choice<(fn(&str) -> ... {parse_login}, ..., ..., ..., ...)>`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mbitutils/src/imcmd.rs:119:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m119\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m/\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m    alt((\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m120\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        parse_login,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m121\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        parse_send,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        parse_logout,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m123\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        parse_exit,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m124\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        parse_quit,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m125\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ))(input)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m______\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m______-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mcall expression requires function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|______|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the full name for the type has been written to '/Users/<USER>/Workspace/rustspace/imspace/target/debug/deps/bitutils-9158ca389b4edb72.long-type-6918928733642237960.txt'\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: consider using `--verbose` to print the full type name to the console\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 2 previous errors","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 2 previous errors\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0618`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about this error, try `rustc --explain E0618`.\u001b[0m\n"}

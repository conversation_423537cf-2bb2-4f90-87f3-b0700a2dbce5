{"rustc": 12610991425282158916, "features": "[\"alloc\", \"default\", \"password-hash\", \"rand\"]", "declared_features": "[\"alloc\", \"default\", \"password-hash\", \"rand\", \"simple\", \"std\", \"zeroize\"]", "target": 5931530492013982456, "profile": 8276155916380437441, "path": 1548762803412460320, "deps": [[6742268975477224606, "password_hash", false, 1449418089224124983], [8700459469608572718, "blake2", false, 8915230836812746629], [11827351784475165436, "base64ct", false, 1221813363585581964]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/argon2-b471fba33be47e37/dep-lib-argon2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 12610991425282158916, "features": "[\"brotli\", \"flate2\", \"gzip\", \"libzstd\", \"tokio\", \"zlib\", \"zstd\", \"zstd-safe\"]", "declared_features": "[\"all\", \"all-algorithms\", \"all-implementations\", \"brotli\", \"bzip2\", \"deflate\", \"deflate64\", \"flate2\", \"futures-io\", \"gzip\", \"libzstd\", \"lz4\", \"lzma\", \"tokio\", \"xz\", \"xz2\", \"zlib\", \"zstd\", \"zstd-safe\", \"zstdmt\"]", "target": 7068030942456847288, "profile": 8276155916380437441, "path": 13884833383467685243, "deps": [[1906322745568073236, "pin_project_lite", false, 18099131382932808050], [3129130049864710036, "memchr", false, 3663423951700227038], [4052408954973158025, "libzstd", false, 9841508385369402104], [7620660491849607393, "futures_core", false, 4005929818739234684], [9538054652646069845, "tokio", false, 13612083831194702672], [9556762810601084293, "brotli", false, 17312408119815542024], [10563170702865159712, "flate2", false, 17676851871807189498], [15788444815745660356, "zstd_safe", false, 17604268004783897579]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/async-compression-5f4c51e9781acddf/dep-lib-async_compression", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
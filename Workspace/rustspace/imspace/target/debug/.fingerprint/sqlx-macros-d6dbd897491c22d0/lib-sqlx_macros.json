{"rustc": 12610991425282158916, "features": "[\"default\", \"derive\", \"json\", \"macros\", \"migrate\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls-aws-lc-rs\", \"_tls-rustls-ring-native-roots\", \"_tls-rustls-ring-webpki\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"derive\", \"ipnet\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlite-unbundled\", \"time\", \"uuid\"]", "target": 13494433325021527976, "profile": 8870715365344955839, "path": 2290840225020064949, "deps": [[3060637413840920116, "proc_macro2", false, 9148073282651010976], [10654871823602349891, "sqlx_macros_core", false, 4888208623958627128], [10776111606377762245, "sqlx_core", false, 8957451752500020817], [17990358020177143287, "quote", false, 8541420211175231035], [18149961000318489080, "syn", false, 8787576699211204106]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-macros-d6dbd897491c22d0/dep-lib-sqlx_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
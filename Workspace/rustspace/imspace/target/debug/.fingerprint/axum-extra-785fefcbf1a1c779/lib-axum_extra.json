{"rustc": 12610991425282158916, "features": "[\"default\", \"tracing\"]", "declared_features": "[\"__private_docs\", \"async-read-body\", \"async-stream\", \"attachment\", \"cookie\", \"cookie-key-expansion\", \"cookie-private\", \"cookie-signed\", \"default\", \"erased-json\", \"error-response\", \"file-stream\", \"form\", \"json-deserializer\", \"json-lines\", \"multipart\", \"protobuf\", \"query\", \"scheme\", \"tracing\", \"typed-header\", \"typed-routing\"]", "target": 4770478002602207591, "profile": 4409898591144521313, "path": 7967221941875667378, "deps": [[784494742817713399, "tower_service", false, 10335948084434898708], [1906322745568073236, "pin_project_lite", false, 18099131382932808050], [5695049318159433696, "tower", false, 17826405543570304617], [7712452662827335977, "tower_layer", false, 10146401121959308989], [7858942147296547339, "rustversion", false, 14908120913300193552], [9010263965687315507, "http", false, 6861923473553998167], [9689903380558560274, "serde", false, 1047605021115382105], [10229185211513642314, "mime", false, 12779884299515262883], [10629569228670356391, "futures_util", false, 14414536708963985533], [13071899967998615733, "axum", false, 9334965768877793544], [14084095096285906100, "http_body", false, 5602413047862799876], [15176407853393882315, "axum_core", false, 12983565820837449165], [16066129441945555748, "bytes", false, 14957974996197214693], [16900715236047033623, "http_body_util", false, 18151372956594295215]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-extra-785fefcbf1a1c779/dep-lib-axum_extra", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
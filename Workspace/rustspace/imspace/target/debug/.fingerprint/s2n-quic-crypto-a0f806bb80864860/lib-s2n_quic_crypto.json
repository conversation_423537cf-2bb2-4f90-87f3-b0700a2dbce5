{"rustc": 12610991425282158916, "features": "[\"default\"]", "declared_features": "[\"aws-lc-bindgen\", \"default\", \"fips\", \"testing\"]", "target": 14443309861834954624, "profile": 8276155916380437441, "path": 15793898236652156548, "deps": [[6528079939221783635, "zeroize", false, 4207080639691995473], [10411997081178400487, "cfg_if", false, 9291184391740713836], [13047422278344534362, "s2n_codec", false, 17391469705488347887], [16944451698427853066, "aws_lc_rs", false, 17068000895915332093], [17258758287242144498, "s2n_quic_core", false, 223759836924404816], [17917672826516349275, "lazy_static", false, 787847552746197140]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/s2n-quic-crypto-a0f806bb80864860/dep-lib-s2n_quic_crypto", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
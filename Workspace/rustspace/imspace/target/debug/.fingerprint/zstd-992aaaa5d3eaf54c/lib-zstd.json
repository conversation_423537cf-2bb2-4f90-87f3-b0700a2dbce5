{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[\"arrays\", \"bindgen\", \"debug\", \"default\", \"doc-cfg\", \"experimental\", \"fat-lto\", \"legacy\", \"no_asm\", \"pkg-config\", \"thin\", \"thin-lto\", \"wasm\", \"zdict_builder\", \"zstdmt\"]", "target": 13967053409313941148, "profile": 8276155916380437441, "path": 17598384884826309294, "deps": [[15788444815745660356, "zstd_safe", false, 17604268004783897579]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/zstd-992aaaa5d3eaf54c/dep-lib-zstd", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
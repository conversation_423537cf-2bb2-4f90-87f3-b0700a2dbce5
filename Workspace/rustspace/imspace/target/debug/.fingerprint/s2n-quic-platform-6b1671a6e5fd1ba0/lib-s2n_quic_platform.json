{"rustc": 12610991425282158916, "features": "[\"default\", \"futures\", \"lazy_static\", \"socket2\", \"std\", \"tokio\", \"tokio-runtime\"]", "declared_features": "[\"bach\", \"bolero-generator\", \"default\", \"futures\", \"generator\", \"io-testing\", \"lazy_static\", \"s2n-quic-xdp\", \"socket2\", \"std\", \"testing\", \"tokio\", \"tokio-runtime\", \"tracing\", \"turmoil\", \"xdp\"]", "target": 786581749991383967, "profile": 15279072984141489520, "path": 15456575996157669078, "deps": [[1397998465184103843, "build_script_build", false, 12343686319124815594], [2706460456408817945, "futures", false, 9225669825067213202], [2924422107542798392, "libc", false, 8303689818531122296], [9538054652646069845, "tokio", false, 13612083831194702672], [10411997081178400487, "cfg_if", false, 9291184391740713836], [12614995553916589825, "socket2", false, 11091046993671588987], [17258758287242144498, "s2n_quic_core", false, 223759836924404816], [17917672826516349275, "lazy_static", false, 787847552746197140]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/s2n-quic-platform-6b1671a6e5fd1ba0/dep-lib-s2n_quic_platform", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 12610991425282158916, "features": "[\"std\"]", "declared_features": "[\"arrays\", \"bindgen\", \"debug\", \"default\", \"doc-cfg\", \"experimental\", \"fat-lto\", \"legacy\", \"no_asm\", \"pkg-config\", \"seekable\", \"std\", \"thin\", \"thin-lto\", \"zdict_builder\", \"zstdmt\"]", "target": 13834647262792939399, "profile": 9000908083626857979, "path": 3860156126881927432, "deps": [[8373447648276846408, "zstd_sys", false, 4606867453482099658], [15788444815745660356, "build_script_build", false, 15624615768251186158]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/zstd-safe-3f99bfc3df5e3eba/dep-lib-zstd_safe", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
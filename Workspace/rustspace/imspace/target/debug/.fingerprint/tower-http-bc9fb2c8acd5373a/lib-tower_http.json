{"rustc": 12610991425282158916, "features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 8276155916380437441, "path": 10084667370588060154, "deps": [[40386456601120721, "percent_encoding", false, 8515805089925031078], [126872836426101300, "async_compression", false, 16104941702988053400], [784494742817713399, "tower_service", false, 10335948084434898708], [1288403060204016458, "tokio_util", false, 5102368922735578052], [1906322745568073236, "pin_project_lite", false, 18099131382932808050], [4121350475192885151, "iri_string", false, 17202745157673420731], [5695049318159433696, "tower", false, 17826405543570304617], [6304235478050270880, "httpdate", false, 11516355879370838166], [7620660491849607393, "futures_core", false, 4005929818739234684], [7712452662827335977, "tower_layer", false, 10146401121959308989], [7896293946984509699, "bitflags", false, 3555078822777601972], [8319709847752024821, "uuid", false, 3718343426925264210], [8606274917505247608, "tracing", false, 12562588181362547402], [9010263965687315507, "http", false, 6861923473553998167], [9538054652646069845, "tokio", false, 13612083831194702672], [10229185211513642314, "mime", false, 12779884299515262883], [10629569228670356391, "futures_util", false, 14414536708963985533], [12475322156296016012, "http_range_header", false, 1324727772577554868], [13077212702700853852, "base64", false, 7440269470791501296], [14084095096285906100, "http_body", false, 5602413047862799876], [16066129441945555748, "bytes", false, 14957974996197214693], [16900715236047033623, "http_body_util", false, 18151372956594295215], [18071510856783138481, "mime_guess", false, 5144148488912101707]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tower-http-bc9fb2c8acd5373a/dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 12610991425282158916, "features": "[\"default\", \"fs\", \"process\", \"signal\"]", "declared_features": "[\"acct\", \"aio\", \"default\", \"dir\", \"env\", \"event\", \"fanotify\", \"feature\", \"fs\", \"hostname\", \"inotify\", \"ioctl\", \"kmod\", \"memoffset\", \"mman\", \"mount\", \"mqueue\", \"net\", \"personality\", \"pin-utils\", \"poll\", \"process\", \"pthread\", \"ptrace\", \"quota\", \"reboot\", \"resource\", \"sched\", \"signal\", \"socket\", \"syslog\", \"term\", \"time\", \"ucontext\", \"uio\", \"user\", \"zerocopy\"]", "target": 1600181213338542824, "profile": 8276155916380437441, "path": 10395488106796695332, "deps": [[2924422107542798392, "libc", false, 8303689818531122296], [5150833351789356492, "build_script_build", false, 11045626345372980942], [7896293946984509699, "bitflags", false, 3555078822777601972], [10411997081178400487, "cfg_if", false, 9291184391740713836]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/nix-e7bec1134ee8078d/dep-lib-nix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
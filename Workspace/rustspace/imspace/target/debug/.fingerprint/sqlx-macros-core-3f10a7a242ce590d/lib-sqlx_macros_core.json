{"rustc": 12610991425282158916, "features": "[\"default\", \"derive\", \"json\", \"macros\", \"migrate\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_sqlite\", \"_tls-native-tls\", \"_tls-rustls-aws-lc-rs\", \"_tls-rustls-ring-native-roots\", \"_tls-rustls-ring-webpki\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"derive\", \"ipnet\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlite-unbundled\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tokio\", \"uuid\"]", "target": 961973412475639632, "profile": 12675354917325698667, "path": 4733598083949528776, "deps": [[530211389790465181, "hex", false, 17552370883555852785], [3060637413840920116, "proc_macro2", false, 9148073282651010976], [3150220818285335163, "url", false, 2250953154919365261], [3405707034081185165, "dotenvy", false, 16037645109875944794], [3722963349756955755, "once_cell", false, 17458791113175105279], [9689903380558560274, "serde", false, 18028552100014019137], [9857275760291862238, "sha2", false, 16623696221740718427], [10776111606377762245, "sqlx_core", false, 8957451752500020817], [12170264697963848012, "either", false, 18098325744976983135], [13077543566650298139, "heck", false, 60162356697184714], [15367738274754116744, "serde_json", false, 3647171519379415044], [17990358020177143287, "quote", false, 8541420211175231035], [18149961000318489080, "syn", false, 8787576699211204106]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-macros-core-3f10a7a242ce590d/dep-lib-sqlx_macros_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 12610991425282158916, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 11259629673856435433, "path": 2523819141247456296, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/winnow-db3a92eac6c24352/dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
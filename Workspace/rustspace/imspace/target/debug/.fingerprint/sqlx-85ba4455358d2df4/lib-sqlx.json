{"rustc": 12610991425282158916, "features": "[\"any\", \"default\", \"derive\", \"json\", \"macros\", \"migrate\", \"sqlx-macros\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_sqlite\", \"_unstable-all-types\", \"all-databases\", \"any\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"default\", \"derive\", \"ipnet\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"regexp\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sqlite\", \"sqlite-preupdate-hook\", \"sqlite-unbundled\", \"sqlx-macros\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tls-native-tls\", \"tls-none\", \"tls-rustls\", \"tls-rustls-aws-lc-rs\", \"tls-rustls-ring\", \"tls-rustls-ring-native-roots\", \"tls-rustls-ring-webpki\", \"uuid\"]", "target": 3003836824758849296, "profile": 8276155916380437441, "path": 11979957872246204357, "deps": [[3276107248499827220, "sqlx_macros", false, 14060677365899502925], [10776111606377762245, "sqlx_core", false, 13517783828610661766]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-85ba4455358d2df4/dep-lib-sqlx", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
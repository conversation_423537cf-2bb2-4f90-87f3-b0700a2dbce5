{"rustc": 12610991425282158916, "features": "[\"ansi_term\", \"atty\", \"color\", \"default\", \"strsim\", \"suggestions\", \"vec_map\"]", "declared_features": "[\"ansi_term\", \"atty\", \"clippy\", \"color\", \"debug\", \"default\", \"doc\", \"nightly\", \"no_cargo\", \"strsim\", \"suggestions\", \"term_size\", \"unstable\", \"vec_map\", \"wrap_help\", \"yaml\", \"yaml-rust\"]", "target": 12198692761336931930, "profile": 8276155916380437441, "path": 3337272995281702976, "deps": [[1322514204948454048, "unicode_width", false, 9890360863774532310], [1810510990979880151, "ansi_term", false, 5023016461347400836], [6485010074357387197, "textwrap", false, 9400698128009991160], [10058577953979766589, "atty", false, 13593304117169209075], [10110425334065384495, "strsim", false, 12776758487444247187], [10435729446543529114, "bitflags", false, 2029322602276294762], [14451951854123638585, "vec_map", false, 4306071263093865475]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-1f58ce40019d28b0/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
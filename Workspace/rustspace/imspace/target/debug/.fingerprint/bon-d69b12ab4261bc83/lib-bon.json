{"rustc": 12610991425282158916, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"experimental-getter\", \"experimental-overwritable\", \"implied-bounds\", \"std\"]", "target": 3061693022905535713, "profile": 16967958134948998885, "path": 11584104407204501432, "deps": [[7858942147296547339, "rustversion", false, 14908120913300193552], [11924877508623646192, "bon_macros", false, 3946206944385065133]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bon-d69b12ab4261bc83/dep-lib-bon", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
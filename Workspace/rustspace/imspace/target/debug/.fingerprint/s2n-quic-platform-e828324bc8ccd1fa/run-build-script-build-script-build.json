{"rustc": 12610991425282158916, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1397998465184103843, "build_script_build", false, 3373330942801844455]], "local": [{"RerunIfChanged": {"output": "debug/build/s2n-quic-platform-e828324bc8ccd1fa/output", "paths": ["features/socket_mmsg.rs", "features/socket_msg.rs"]}}, {"RerunIfEnvChanged": {"var": "S2N_QUIC_PLATFORM_FEATURES_OVERRIDE", "val": null}}, {"RerunIfEnvChanged": {"var": "RUSTC", "val": null}}, {"RerunIfEnvChanged": {"var": "OUT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_TARGET_OS", "val": null}}, {"RerunIfEnvChanged": {"var": "RUSTC_LINKER", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}
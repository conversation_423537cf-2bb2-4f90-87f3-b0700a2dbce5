{"rustc": 12610991425282158916, "features": "[\"default\"]", "declared_features": "[\"default\", \"experimental-overwritable\", \"implied-bounds\"]", "target": 3938351697300514280, "profile": 1128122060340104463, "path": 2749574926730044516, "deps": [[496455418292392305, "darling", false, 5254528024621102941], [3060637413840920116, "proc_macro2", false, 9148073282651010976], [7858942147296547339, "rustversion", false, 14908120913300193552], [15383437925411509181, "ident_case", false, 9972968139937508567], [16768685902412194232, "prettyplease", false, 8131459110093133826], [17990358020177143287, "quote", false, 8541420211175231035], [18149961000318489080, "syn", false, 8787576699211204106]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bon-macros-3b2a965d0fcd91f5/dep-lib-bon_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 12610991425282158916, "features": "[\"default\", \"internal\", \"quic\"]", "declared_features": "[\"cmake\", \"default\", \"fips\", \"internal\", \"pq\", \"quic\", \"stacktrace\", \"unstable-cert_authorities\", \"unstable-cleanup\", \"unstable-crl\", \"unstable-fingerprint\", \"unstable-ktls\", \"unstable-npn\", \"unstable-renegotiate\"]", "target": 10446624290008948919, "profile": 8276155916380437441, "path": 988202325059636913, "deps": [[2924422107542798392, "libc", false, 8303689818531122296], [10087617178487351952, "build_script_build", false, 2435034668110998509], [16944451698427853066, "aws_lc_rs", false, 17068000895915332093]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/s2n-tls-sys-0b9d0055345df09a/dep-lib-s2n_tls_sys", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
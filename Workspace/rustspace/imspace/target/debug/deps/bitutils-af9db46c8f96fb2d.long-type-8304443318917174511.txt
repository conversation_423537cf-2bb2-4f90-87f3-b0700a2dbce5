Choice<(for<'a> fn(&'a str) -> Result<(&'a str, IMCommand), nom::Err<nom::error::Error<&'a str>>> {parse_login}, for<'a> fn(&'a str) -> Result<(&'a str, IMCommand), nom::Err<nom::error::Error<&'a str>>> {parse_send}, for<'a> fn(&'a str) -> Result<(&'a str, IMCommand), nom::Err<nom::error::Error<&'a str>>> {parse_logout}, for<'a> fn(&'a str) -> Result<(&'a str, IMCommand), nom::Err<nom::error::Error<&'a str>>> {parse_exit}, for<'a> fn(&'a str) -> Result<(&'a str, IMCommand), nom::Err<nom::error::<PERSON>rror<&'a str>>> {parse_quit})>

warning: unused doc comment
  --> features/socket_mmsg.rs:13:1
   |
13 | / /// Try to resolve the required references from the linker
14 | | ///
15 | | /// The build will fail if they don't exist.
   | |____________________________________________^
16 |   #[cfg(all(unix, feature = "std"))]
17 | / extern "C" {
18 | |     #[link_name = "sendmmsg"]
19 | |     static SENDMMSG: *const u8;
20 | |     #[link_name = "recvmmsg"]
21 | |     static RECVMMSG: *const u8;
22 | | }
   | |_- rustdoc does not generate documentation for extern blocks
   |
   = help: use `//` for a plain comment
   = note: `#[warn(unused_doc_comments)]` on by default

error: linking with `cc` failed: exit status: 1
  |
  = note:  "cc" "/var/folders/nx/x13c73192n3fj1jtcqw8w8pm0000gn/T/rustcAkEGl0/symbols.o" "<2 object files omitted>" "<sysroot>/lib/rustlib/aarch64-apple-darwin/lib/{libstd-*,libpanic_unwind-*,libobject-*,libmemchr-*,libaddr2line-*,libgimli-*,librustc_demangle-*,libstd_detect-*,libhashbrown-*,librustc_std_workspace_alloc-*,libminiz_oxide-*,libadler2-*,libunwind-*,libcfg_if-*,liblibc-*,liballoc-*,librustc_std_workspace_core-*,libcore-*,libcompiler_builtins-*}.rlib" "-lSystem" "-lc" "-lm" "-arch" "arm64" "-mmacosx-version-min=11.0.0" "-o" "/Users/<USER>/Workspace/rustspace/imspace/target/debug/build/s2n-quic-platform-e828324bc8ccd1fa/out/socket_mmsg" "-Wl,-dead_strip" "-nodefaultlibs"
  = note: some arguments are omitted. use `--verbose` to show all linker arguments
  = note: Undefined symbols for architecture arm64:
            "_recvmmsg", referenced from:
                socket_mmsg::main::he9099f7182d43b1e in socket_mmsg.socket_mmsg.4cc5bf38bd425a54-cgu.0.rcgu.o
                socket_mmsg::main::he9099f7182d43b1e in socket_mmsg.socket_mmsg.4cc5bf38bd425a54-cgu.0.rcgu.o
            "_sendmmsg", referenced from:
                socket_mmsg::main::he9099f7182d43b1e in socket_mmsg.socket_mmsg.4cc5bf38bd425a54-cgu.0.rcgu.o
                socket_mmsg::main::he9099f7182d43b1e in socket_mmsg.socket_mmsg.4cc5bf38bd425a54-cgu.0.rcgu.o
          ld: symbol(s) not found for architecture arm64
          clang: error: linker command failed with exit code 1 (use -v to see invocation)
          

error: aborting due to 1 previous error; 1 warning emitted

warning: unused doc comment
  --> features/socket_msg.rs:13:1
   |
13 | / /// Try to resolve the required references from the linker
14 | | ///
15 | | /// The build will fail if they don't exist.
   | |____________________________________________^
16 |   #[cfg(all(unix, feature = "std"))]
17 | / extern "C" {
18 | |     #[link_name = "sendmsg"]
19 | |     static SENDMSG: *const u8;
20 | |     #[link_name = "recvmsg"]
21 | |     static RECVMSG: *const u8;
22 | | }
   | |_- rustdoc does not generate documentation for extern blocks
   |
   = help: use `//` for a plain comment
   = note: `#[warn(unused_doc_comments)]` on by default

warning: 1 warning emitted


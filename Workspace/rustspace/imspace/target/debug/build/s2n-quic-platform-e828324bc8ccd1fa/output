cargo:rerun-if-env-changed=S2N_QUIC_PLATFORM_FEATURES_OVERRIDE
cargo:rerun-if-env-changed=RUSTC
cargo:rerun-if-env-changed=OUT_DIR
cargo:rerun-if-env-changed=TARGET
cargo:rerun-if-env-changed=CARGO_CFG_TARGET_OS
cargo:rerun-if-env-changed=RUSTC_LINKER
cargo:rerun-if-changed=features/socket_mmsg.rs
cargo:rerun-if-changed=features/socket_msg.rs
cargo:rustc-cfg=s2n_quic_platform_cmsg
cargo:rustc-cfg=s2n_quic_platform_socket_msg
cargo:rustc-cfg=s2n_quic_platform_pktinfo
cargo:rustc-cfg=s2n_quic_platform_tos

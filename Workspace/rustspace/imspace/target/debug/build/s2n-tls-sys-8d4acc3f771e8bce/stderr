=== Testing feature S2N_ATOMIC_SUPPORTED ===
S2N_ATOMIC_SUPPORTED: true
=== Testing feature S2N_CLOEXEC_SUPPORTED ===
S2N_CLOEXEC_SUPPORTED: true
=== Testing feature S2N_CLOEXEC_XOPEN_SUPPORTED ===
S2N_CLOEXEC_XOPEN_SUPPORTED: true
=== Testing feature S2N_CLONE_SUPPORTED ===
lib/tests/features/S2N_CLONE_SUPPORTED.c:22:5: error: call to undeclared function 'clone'; ISO C99 and later do not support implicit function declarations [-Wimplicit-function-declaration]
   22 |     clone(NULL, NULL, 0, NULL);
      |     ^
1 error generated.
S2N_CLONE_SUPPORTED: false
=== Testing feature S2N_COMPILER_SUPPORTS_BRANCH_ALIGN ===
clang: error: unsupported argument '-mbranches-within-32B-boundaries' to option '-Wa,'
S2N_COMPILER_SUPPORTS_BRANCH_ALIGN: false
=== Testing feature S2N_CPUID_AVAILABLE ===
In file included from lib/tests/features/S2N_CPUID_AVAILABLE.c:16:
/Library/Developer/CommandLineTools/usr/lib/clang/17/include/cpuid.h:14:2: error: this header is for x86 only
   14 | #error this header is for x86 only
      |  ^
/Library/Developer/CommandLineTools/usr/lib/clang/17/include/cpuid.h:309:5: error: invalid output constraint '=a' in asm
  309 |     __cpuid(__leaf, __eax, __ebx, __ecx, __edx);
      |     ^
/Library/Developer/CommandLineTools/usr/lib/clang/17/include/cpuid.h:273:11: note: expanded from macro '__cpuid'
  273 |         : "=a"(__eax), "=r" (__ebx), "=c"(__ecx), "=d"(__edx) \
      |           ^
/Library/Developer/CommandLineTools/usr/lib/clang/17/include/cpuid.h:324:5: error: invalid output constraint '=a' in asm
  324 |     __cpuid(__leaf, *__eax, *__ebx, *__ecx, *__edx);
      |     ^
/Library/Developer/CommandLineTools/usr/lib/clang/17/include/cpuid.h:273:11: note: expanded from macro '__cpuid'
  273 |         : "=a"(__eax), "=r" (__ebx), "=c"(__ecx), "=d"(__edx) \
      |           ^
/Library/Developer/CommandLineTools/usr/lib/clang/17/include/cpuid.h:338:5: error: invalid output constraint '=a' in asm
  338 |     __cpuid_count(__leaf, __subleaf, *__eax, *__ebx, *__ecx, *__edx);
      |     ^
/Library/Developer/CommandLineTools/usr/lib/clang/17/include/cpuid.h:280:11: note: expanded from macro '__cpuid_count'
  280 |         : "=a"(__eax), "=r" (__ebx), "=c"(__ecx), "=d"(__edx) \
      |           ^
/Library/Developer/CommandLineTools/usr/lib/clang/17/include/cpuid.h:346:3: error: invalid output constraint '=a' in asm
  346 |   __cpuid_count(__leaf, __subleaf, __cpu_info[0], __cpu_info[1], __cpu_info[2],
      |   ^
/Library/Developer/CommandLineTools/usr/lib/clang/17/include/cpuid.h:280:11: note: expanded from macro '__cpuid_count'
  280 |         : "=a"(__eax), "=r" (__ebx), "=c"(__ecx), "=d"(__edx) \
      |           ^
5 errors generated.
S2N_CPUID_AVAILABLE: false
=== Testing feature S2N_DIAGNOSTICS_POP_SUPPORTED ===
S2N_DIAGNOSTICS_POP_SUPPORTED: true
=== Testing feature S2N_DIAGNOSTICS_PUSH_SUPPORTED ===
S2N_DIAGNOSTICS_PUSH_SUPPORTED: true
=== Testing feature S2N_EXECINFO_AVAILABLE ===
S2N_EXECINFO_AVAILABLE: true
=== Testing feature S2N_FALL_THROUGH_SUPPORTED ===
S2N_FALL_THROUGH_SUPPORTED: true
=== Testing feature S2N_FEATURES_AVAILABLE ===
lib/tests/features/S2N_FEATURES_AVAILABLE.c:16:10: fatal error: 'features.h' file not found
   16 | #include <features.h>
      |          ^~~~~~~~~~~~
1 error generated.
S2N_FEATURES_AVAILABLE: false
=== Testing feature S2N_KTLS_SUPPORTED ===
lib/tests/features/S2N_KTLS_SUPPORTED.c:30:24: error: use of undeclared identifier 'TLS_1_2_VERSION'
   30 |     int versions[] = { TLS_1_2_VERSION, TLS_1_3_VERSION };
      |                        ^
lib/tests/features/S2N_KTLS_SUPPORTED.c:30:41: error: use of undeclared identifier 'TLS_1_3_VERSION'
   30 |     int versions[] = { TLS_1_2_VERSION, TLS_1_3_VERSION };
      |                                         ^
lib/tests/features/S2N_KTLS_SUPPORTED.c:31:28: error: use of undeclared identifier 'TLS_CIPHER_AES_GCM_128'
   31 |     int cipher_types[] = { TLS_CIPHER_AES_GCM_128, TLS_CIPHER_AES_GCM_256 };
      |                            ^
lib/tests/features/S2N_KTLS_SUPPORTED.c:31:52: error: use of undeclared identifier 'TLS_CIPHER_AES_GCM_256'
   31 |     int cipher_types[] = { TLS_CIPHER_AES_GCM_128, TLS_CIPHER_AES_GCM_256 };
      |                                                    ^
lib/tests/features/S2N_KTLS_SUPPORTED.c:33:42: error: variable has incomplete type 'struct tls12_crypto_info_aes_gcm_128'
   33 |     struct tls12_crypto_info_aes_gcm_128 aes_crypto_info_128 = { 0 };
      |                                          ^
lib/tests/features/S2N_KTLS_SUPPORTED.c:33:12: note: forward declaration of 'struct tls12_crypto_info_aes_gcm_128'
   33 |     struct tls12_crypto_info_aes_gcm_128 aes_crypto_info_128 = { 0 };
      |            ^
lib/tests/features/S2N_KTLS_SUPPORTED.c:34:42: error: variable has incomplete type 'struct tls12_crypto_info_aes_gcm_256'
   34 |     struct tls12_crypto_info_aes_gcm_256 aes_crypto_info_256 = { 0 };
      |                                          ^
lib/tests/features/S2N_KTLS_SUPPORTED.c:34:12: note: forward declaration of 'struct tls12_crypto_info_aes_gcm_256'
   34 |     struct tls12_crypto_info_aes_gcm_256 aes_crypto_info_256 = { 0 };
      |            ^
lib/tests/features/S2N_KTLS_SUPPORTED.c:35:26: error: use of undeclared identifier 'TLS_GET_RECORD_TYPE'
   35 |     int operations[] = { TLS_GET_RECORD_TYPE, TLS_SET_RECORD_TYPE };
      |                          ^
lib/tests/features/S2N_KTLS_SUPPORTED.c:35:47: error: use of undeclared identifier 'TLS_SET_RECORD_TYPE'
   35 |     int operations[] = { TLS_GET_RECORD_TYPE, TLS_SET_RECORD_TYPE };
      |                                               ^
8 errors generated.
S2N_KTLS_SUPPORTED: false
=== Testing feature S2N_LIBCRYPTO_SUPPORTS_EC_KEY_CHECK_FIPS ===
S2N_LIBCRYPTO_SUPPORTS_EC_KEY_CHECK_FIPS: true
=== Testing feature S2N_LIBCRYPTO_SUPPORTS_ENGINE ===
lib/tests/features/S2N_LIBCRYPTO_SUPPORTS_ENGINE.c:36:34: warning: unused parameter 'buf' [-Wunused-parameter]
   36 | int s2n_noop_rand(unsigned char *buf, int num)
      |                                  ^
lib/tests/features/S2N_LIBCRYPTO_SUPPORTS_ENGINE.c:36:43: warning: unused parameter 'num' [-Wunused-parameter]
   36 | int s2n_noop_rand(unsigned char *buf, int num)
      |                                           ^
lib/tests/features/S2N_LIBCRYPTO_SUPPORTS_ENGINE.c:45:5: error: call to undeclared function 'ENGINE_set_id'; ISO C99 and later do not support implicit function declarations [-Wimplicit-function-declaration]
   45 |     ENGINE_set_id(e, "id");
      |     ^
lib/tests/features/S2N_LIBCRYPTO_SUPPORTS_ENGINE.c:46:5: error: call to undeclared function 'ENGINE_set_name'; ISO C99 and later do not support implicit function declarations [-Wimplicit-function-declaration]
   46 |     ENGINE_set_name(e, "name");
      |     ^
lib/tests/features/S2N_LIBCRYPTO_SUPPORTS_ENGINE.c:47:5: error: call to undeclared function 'ENGINE_set_flags'; ISO C99 and later do not support implicit function declarations [-Wimplicit-function-declaration]
   47 |     ENGINE_set_flags(e, ENGINE_FLAGS_NO_REGISTER_ALL);
      |     ^
lib/tests/features/S2N_LIBCRYPTO_SUPPORTS_ENGINE.c:47:25: error: use of undeclared identifier 'ENGINE_FLAGS_NO_REGISTER_ALL'
   47 |     ENGINE_set_flags(e, ENGINE_FLAGS_NO_REGISTER_ALL);
      |                         ^
lib/tests/features/S2N_LIBCRYPTO_SUPPORTS_ENGINE.c:48:5: error: call to undeclared function 'ENGINE_set_init_function'; ISO C99 and later do not support implicit function declarations [-Wimplicit-function-declaration]
   48 |     ENGINE_set_init_function(e, NULL);
      |     ^
lib/tests/features/S2N_LIBCRYPTO_SUPPORTS_ENGINE.c:49:5: error: call to undeclared function 'ENGINE_set_RAND'; ISO C99 and later do not support implicit function declarations [-Wimplicit-function-declaration]
   49 |     ENGINE_set_RAND(e, NULL);
      |     ^
lib/tests/features/S2N_LIBCRYPTO_SUPPORTS_ENGINE.c:50:5: error: call to undeclared function 'ENGINE_add'; ISO C99 and later do not support implicit function declarations [-Wimplicit-function-declaration]
   50 |     ENGINE_add(e);
      |     ^
lib/tests/features/S2N_LIBCRYPTO_SUPPORTS_ENGINE.c:51:5: error: call to undeclared function 'ENGINE_init'; ISO C99 and later do not support implicit function declarations [-Wimplicit-function-declaration]
   51 |     ENGINE_init(e);
      |     ^
lib/tests/features/S2N_LIBCRYPTO_SUPPORTS_ENGINE.c:52:5: error: call to undeclared function 'ENGINE_set_default'; ISO C99 and later do not support implicit function declarations [-Wimplicit-function-declaration]
   52 |     ENGINE_set_default(e, ENGINE_METHOD_RAND);
      |     ^
lib/tests/features/S2N_LIBCRYPTO_SUPPORTS_ENGINE.c:52:27: error: use of undeclared identifier 'ENGINE_METHOD_RAND'; did you mean 'ENGINE_set_RAND'?
   52 |     ENGINE_set_default(e, ENGINE_METHOD_RAND);
      |                           ^~~~~~~~~~~~~~~~~~
      |                           ENGINE_set_RAND
lib/tests/features/S2N_LIBCRYPTO_SUPPORTS_ENGINE.c:49:5: note: 'ENGINE_set_RAND' declared here
   49 |     ENGINE_set_RAND(e, NULL);
      |     ^
lib/tests/features/S2N_LIBCRYPTO_SUPPORTS_ENGINE.c:55:5: error: call to undeclared function 'ENGINE_remove'; ISO C99 and later do not support implicit function declarations [-Wimplicit-function-declaration]
   55 |     ENGINE_remove(e);
      |     ^
lib/tests/features/S2N_LIBCRYPTO_SUPPORTS_ENGINE.c:56:5: error: call to undeclared function 'ENGINE_finish'; ISO C99 and later do not support implicit function declarations [-Wimplicit-function-declaration]
   56 |     ENGINE_finish(e);
      |     ^
lib/tests/features/S2N_LIBCRYPTO_SUPPORTS_ENGINE.c:57:5: error: call to undeclared function 'ENGINE_unregister_RAND'; ISO C99 and later do not support implicit function declarations [-Wimplicit-function-declaration]
   57 |     ENGINE_unregister_RAND(e);
      |     ^
lib/tests/features/S2N_LIBCRYPTO_SUPPORTS_ENGINE.c:60:5: error: call to undeclared function 'RAND_set_rand_engine'; ISO C99 and later do not support implicit function declarations [-Wimplicit-function-declaration]
   60 |     RAND_set_rand_engine(NULL);
      |     ^
lib/tests/features/S2N_LIBCRYPTO_SUPPORTS_ENGINE.c:71:18: error: incompatible function pointer types initializing 'int (*)(uint8_t *, size_t)' (aka 'int (*)(unsigned char *, unsigned long)') with an expression of type 'int (unsigned char *, int)' [-Wincompatible-function-pointer-types]
   71 |         .bytes = s2n_noop_rand,
      |                  ^~~~~~~~~~~~~
lib/tests/features/S2N_LIBCRYPTO_SUPPORTS_ENGINE.c:74:23: error: incompatible function pointer types initializing 'int (*)(uint8_t *, size_t)' (aka 'int (*)(unsigned char *, unsigned long)') with an expression of type 'int (unsigned char *, int)' [-Wincompatible-function-pointer-types]
   74 |         .pseudorand = s2n_noop_rand,
      |                       ^~~~~~~~~~~~~
2 warnings and 16 errors generated.
S2N_LIBCRYPTO_SUPPORTS_ENGINE: false
=== Testing feature S2N_LIBCRYPTO_SUPPORTS_EVP_AEAD_TLS ===
S2N_LIBCRYPTO_SUPPORTS_EVP_AEAD_TLS: true
=== Testing feature S2N_LIBCRYPTO_SUPPORTS_EVP_KEM ===
S2N_LIBCRYPTO_SUPPORTS_EVP_KEM: true
=== Testing feature S2N_LIBCRYPTO_SUPPORTS_EVP_MD5_SHA1_HASH ===
S2N_LIBCRYPTO_SUPPORTS_EVP_MD5_SHA1_HASH: true
=== Testing feature S2N_LIBCRYPTO_SUPPORTS_EVP_MD_CTX_SET_PKEY_CTX ===
S2N_LIBCRYPTO_SUPPORTS_EVP_MD_CTX_SET_PKEY_CTX: true
=== Testing feature S2N_LIBCRYPTO_SUPPORTS_EVP_RC4 ===
S2N_LIBCRYPTO_SUPPORTS_EVP_RC4: true
=== Testing feature S2N_LIBCRYPTO_SUPPORTS_FLAG_NO_CHECK_TIME ===
S2N_LIBCRYPTO_SUPPORTS_FLAG_NO_CHECK_TIME: true
=== Testing feature S2N_LIBCRYPTO_SUPPORTS_HKDF ===
S2N_LIBCRYPTO_SUPPORTS_HKDF: true
=== Testing feature S2N_LIBCRYPTO_SUPPORTS_MLDSA ===
S2N_LIBCRYPTO_SUPPORTS_MLDSA: true
=== Testing feature S2N_LIBCRYPTO_SUPPORTS_MLKEM ===
S2N_LIBCRYPTO_SUPPORTS_MLKEM: true
=== Testing feature S2N_LIBCRYPTO_SUPPORTS_PRIVATE_RAND ===
S2N_LIBCRYPTO_SUPPORTS_PRIVATE_RAND: true
=== Testing feature S2N_LIBCRYPTO_SUPPORTS_PROVIDERS ===
lib/tests/features/S2N_LIBCRYPTO_SUPPORTS_PROVIDERS.c:28:18: error: call to undeclared function 'EVP_MD_fetch'; ISO C99 and later do not support implicit function declarations [-Wimplicit-function-declaration]
   28 |     EVP_MD *md = EVP_MD_fetch(NULL, NULL, NULL);
      |                  ^
lib/tests/features/S2N_LIBCRYPTO_SUPPORTS_PROVIDERS.c:28:13: error: incompatible integer to pointer conversion initializing 'EVP_MD *' (aka 'struct env_md_st *') with an expression of type 'int' [-Wint-conversion]
   28 |     EVP_MD *md = EVP_MD_fetch(NULL, NULL, NULL);
      |             ^    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
lib/tests/features/S2N_LIBCRYPTO_SUPPORTS_PROVIDERS.c:29:5: error: call to undeclared function 'EVP_MD_free'; ISO C99 and later do not support implicit function declarations [-Wimplicit-function-declaration]
   29 |     EVP_MD_free(md);
      |     ^
lib/tests/features/S2N_LIBCRYPTO_SUPPORTS_PROVIDERS.c:32:30: error: call to undeclared function 'EVP_PKEY_CTX_new_from_pkey'; ISO C99 and later do not support implicit function declarations [-Wimplicit-function-declaration]
   32 |     EVP_PKEY_CTX *pkey_ctx = EVP_PKEY_CTX_new_from_pkey(NULL, NULL, NULL);
      |                              ^
lib/tests/features/S2N_LIBCRYPTO_SUPPORTS_PROVIDERS.c:32:19: error: incompatible integer to pointer conversion initializing 'EVP_PKEY_CTX *' (aka 'struct evp_pkey_ctx_st *') with an expression of type 'int' [-Wint-conversion]
   32 |     EVP_PKEY_CTX *pkey_ctx = EVP_PKEY_CTX_new_from_pkey(NULL, NULL, NULL);
      |                   ^          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
5 errors generated.
S2N_LIBCRYPTO_SUPPORTS_PROVIDERS: false
=== Testing feature S2N_LIBCRYPTO_SUPPORTS_RSA_PSS_SIGNING ===
S2N_LIBCRYPTO_SUPPORTS_RSA_PSS_SIGNING: true
=== Testing feature S2N_LIBCRYPTO_SUPPORTS_SHAKE ===
S2N_LIBCRYPTO_SUPPORTS_SHAKE: true
=== Testing feature S2N_LIBCRYPTO_SUPPORTS_X509_STORE_LIST ===
S2N_LIBCRYPTO_SUPPORTS_X509_STORE_LIST: true
=== Testing feature S2N_LINUX_SENDFILE ===
lib/tests/features/S2N_LINUX_SENDFILE.c:21:10: fatal error: 'sys/sendfile.h' file not found
   21 | #include <sys/sendfile.h>
      |          ^~~~~~~~~~~~~~~~
1 error generated.
S2N_LINUX_SENDFILE: false
=== Testing feature S2N_MADVISE_SUPPORTED ===
S2N_MADVISE_SUPPORTED: true
=== Testing feature S2N_MINHERIT_SUPPORTED ===
S2N_MINHERIT_SUPPORTED: true

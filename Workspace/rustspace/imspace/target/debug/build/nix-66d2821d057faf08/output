cargo:rustc-check-cfg=cfg(android)
cargo:rustc-check-cfg=cfg(dragonfly)
cargo:rustc-check-cfg=cfg(ios)
cargo:rustc-check-cfg=cfg(freebsd)
cargo:rustc-check-cfg=cfg(illumos)
cargo:rustc-check-cfg=cfg(linux)
cargo:rustc-check-cfg=cfg(macos)
cargo:rustc-cfg=macos
cargo:rustc-check-cfg=cfg(netbsd)
cargo:rustc-check-cfg=cfg(openbsd)
cargo:rustc-check-cfg=cfg(solaris)
cargo:rustc-check-cfg=cfg(watchos)
cargo:rustc-check-cfg=cfg(tvos)
cargo:rustc-check-cfg=cfg(visionos)
cargo:rustc-check-cfg=cfg(apple_targets)
cargo:rustc-cfg=apple_targets
cargo:rustc-check-cfg=cfg(bsd)
cargo:rustc-cfg=bsd
cargo:rustc-check-cfg=cfg(bsd_without_apple)
cargo:rustc-check-cfg=cfg(linux_android)
cargo:rustc-check-cfg=cfg(freebsdlike)
cargo:rustc-check-cfg=cfg(netbsdlike)
cargo:rustc-check-cfg=cfg(solarish)
cargo:rustc-check-cfg=cfg(fbsd14)
cargo:rustc-check-cfg=cfg(qemu)
cargo:rustc-check-cfg=cfg(target_os, values("cygwin"))
